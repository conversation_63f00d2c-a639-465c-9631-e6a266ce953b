[build]
  command = "npm run build"
  publish = "dist"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.mjs"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Don't redirect static assets
[[redirects]]
  from = "/assets/*"
  to = "/assets/:splat"
  status = 200

# Don't redirect JS files
[[redirects]]
  from = "/*.js"
  to = "/:splat"
  status = 200

# Don't redirect CSS files
[[redirects]]
  from = "/*.css"
  to = "/:splat"
  status = 200

# SPA fallback for all other routes
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200