import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import communityReducer from './slices/communitySlice';
import postReducer from './slices/postSlice';
import notificationReducer from './slices/notificationSlice';
import chatReducer from './slices/chatSlice';
import profileReducer from './slices/profileSlice';
import exploreReducer from './slices/exploreSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    communities: communityReducer,
    posts: postReducer,
    notifications: notificationReducer,
    chat: chatReducer,
    profile: profileReducer,
    explore: exploreReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});


