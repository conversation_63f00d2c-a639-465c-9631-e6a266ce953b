{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "framework": "vite", "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.js)$", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}]}, {"source": "/(.*\\.mjs)$", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}]}]}